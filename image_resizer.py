#!/usr/bin/env python3
"""
Image Resizer Tool - Resize images to 1280x720 using OpenCV
Công cụ resize ảnh về kích thước 1280x720 sử dụng OpenCV
"""

import cv2
import os
import sys
import numpy as np
from pathlib import Path

def add_morning_vibes_text(image):
    """
    Thêm chữ "Morning Vibes" vào ảnh

    Args:
        image: Ảnh OpenCV (numpy array)

    Returns:
        image: Ảnh đã thêm chữ
    """
    # Lấy kích thước ảnh
    height, width = image.shape[:2]

    # Cấu hình text
    text = "Morning Vibes"
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 2.5  # Kích thước font lớn cho đẹp
    color = (255, 255, 255)  # Màu trắng
    thickness = 4  # Độ đậm

    # Tính toán kích thước text
    (text_width, text_height), baseline = cv2.getTextSize(text, font, font_scale, thickness)

    # Vị trí text: gi<PERSON><PERSON> the<PERSON> chi<PERSON>, đ<PERSON><PERSON> lên trên 100 pixel
    x = (width - text_width) // 2
    y = (height // 2) - 100 + text_height // 2

    # Đảm bảo text không bị cắt
    if y < text_height:
        y = text_height + 10
    if y > height - 10:
        y = height - 10

    # Thêm shadow/outline cho text để nổi bật hơn
    shadow_offset = 3
    shadow_color = (0, 0, 0)  # Màu đen cho shadow

    # Vẽ shadow
    cv2.putText(image, text, (x + shadow_offset, y + shadow_offset),
                font, font_scale, shadow_color, thickness + 1, cv2.LINE_AA)

    # Vẽ text chính
    cv2.putText(image, text, (x, y), font, font_scale, color, thickness, cv2.LINE_AA)

    return image

def resize_image(input_path, output_path, width=1280, height=720, keep_aspect_ratio=False, add_text=True):
    """
    Resize ảnh về kích thước mong muốn và thêm chữ "Morning Vibes"

    Args:
        input_path (str): Đường dẫn ảnh đầu vào
        output_path (str): Đường dẫn ảnh đầu ra
        width (int): Chiều rộng mong muốn (default: 1280)
        height (int): Chiều cao mong muốn (default: 720)
        keep_aspect_ratio (bool): Giữ tỷ lệ khung hình (default: False)
        add_text (bool): Thêm chữ "Morning Vibes" (default: True)

    Returns:
        bool: True nếu thành công, False nếu thất bại
    """
    try:
        # Đọc ảnh
        image = cv2.imread(input_path)
        
        if image is None:
            print(f"❌ Không thể đọc ảnh: {input_path}")
            return False
        
        # Lấy kích thước ảnh gốc
        original_height, original_width = image.shape[:2]
        print(f"📏 Kích thước gốc: {original_width}x{original_height}")
        
        if keep_aspect_ratio:
            # Tính toán kích thước mới giữ nguyên tỷ lệ
            aspect_ratio = original_width / original_height
            target_aspect_ratio = width / height
            
            if aspect_ratio > target_aspect_ratio:
                # Ảnh rộng hơn, fit theo chiều rộng
                new_width = width
                new_height = int(width / aspect_ratio)
            else:
                # Ảnh cao hơn, fit theo chiều cao
                new_height = height
                new_width = int(height * aspect_ratio)
            
            print(f"📐 Kích thước mới (giữ tỷ lệ): {new_width}x{new_height}")
        else:
            # Resize trực tiếp về kích thước mong muốn
            new_width = width
            new_height = height
            print(f"📐 Kích thước mới: {new_width}x{new_height}")
        
        # Resize ảnh
        resized_image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)

        # Thêm chữ "Morning Vibes" nếu được yêu cầu
        if add_text:
            resized_image = add_morning_vibes_text(resized_image)
            print("✨ Đã thêm chữ 'Morning Vibes' vào ảnh")

        # Tạo thư mục output nếu chưa tồn tại
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # Lưu ảnh
        success = cv2.imwrite(output_path, resized_image)
        
        if success:
            print(f"✅ Resize thành công: {output_path}")
            return True
        else:
            print(f"❌ Không thể lưu ảnh: {output_path}")
            return False
            
    except Exception as e:
        print(f"❌ Lỗi khi resize ảnh: {str(e)}")
        return False

def resize_single_image(input_path, output_path=None, keep_aspect_ratio=False, add_text=True):
    """
    Resize một ảnh đơn lẻ và thêm chữ "Morning Vibes"

    Args:
        input_path (str): Đường dẫn ảnh đầu vào
        output_path (str): Đường dẫn ảnh đầu ra (optional)
        keep_aspect_ratio (bool): Giữ tỷ lệ khung hình
        add_text (bool): Thêm chữ "Morning Vibes" (default: True)
    """
    if not os.path.exists(input_path):
        print(f"❌ File không tồn tại: {input_path}")
        return False
    
    # Tạo tên file output nếu không được cung cấp
    if output_path is None:
        path_obj = Path(input_path)
        output_path = str(path_obj.parent / f"{path_obj.stem}_1280x720{path_obj.suffix}")
    
    print(f"🖼️  Đang resize: {input_path}")
    return resize_image(input_path, output_path, keep_aspect_ratio=keep_aspect_ratio, add_text=add_text)

def resize_batch_images(input_folder, output_folder=None, keep_aspect_ratio=False, add_text=True):
    """
    Resize hàng loạt ảnh trong thư mục và thêm chữ "Morning Vibes"

    Args:
        input_folder (str): Thư mục chứa ảnh đầu vào
        output_folder (str): Thư mục đầu ra (optional)
        keep_aspect_ratio (bool): Giữ tỷ lệ khung hình
        add_text (bool): Thêm chữ "Morning Vibes" (default: True)
    """
    if not os.path.exists(input_folder):
        print(f"❌ Thư mục không tồn tại: {input_folder}")
        return
    
    # Tạo thư mục output nếu không được cung cấp
    if output_folder is None:
        output_folder = os.path.join(input_folder, "resized_1280x720")
    
    # Tạo thư mục output
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
    
    # Các định dạng ảnh được hỗ trợ
    supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.webp'}
    
    # Tìm tất cả file ảnh
    image_files = []
    for file in os.listdir(input_folder):
        if Path(file).suffix.lower() in supported_formats:
            image_files.append(file)
    
    if not image_files:
        print(f"❌ Không tìm thấy ảnh nào trong thư mục: {input_folder}")
        return
    
    print(f"📁 Tìm thấy {len(image_files)} ảnh để resize")
    
    success_count = 0
    for i, filename in enumerate(image_files, 1):
        input_path = os.path.join(input_folder, filename)
        output_path = os.path.join(output_folder, filename)
        
        print(f"\n[{i}/{len(image_files)}] Đang xử lý: {filename}")

        if resize_image(input_path, output_path, keep_aspect_ratio=keep_aspect_ratio, add_text=add_text):
            success_count += 1
    
    print(f"\n🎉 Hoàn thành! Resize thành công {success_count}/{len(image_files)} ảnh")
    print(f"📂 Ảnh đã được lưu tại: {output_folder}")

def main():
    """Hàm main để chạy chương trình"""
    print("🖼️  IMAGE RESIZER - Resize ảnh về 1280x720 + Morning Vibes")
    print("=" * 60)

    if len(sys.argv) < 2:
        print("Cách sử dụng:")
        print("  python image_resizer.py <đường_dẫn_ảnh>                    # Resize 1 ảnh + thêm text")
        print("  python image_resizer.py <thư_mục_ảnh> --batch              # Resize hàng loạt + thêm text")
        print("  python image_resizer.py <đường_dẫn> --keep-aspect          # Giữ tỷ lệ khung hình")
        print("  python image_resizer.py <đường_dẫn> --no-text              # Không thêm chữ 'Morning Vibes'")
        print("  python image_resizer.py <đường_dẫn> --output <đầu_ra>      # Chỉ định file/thư mục đầu ra")
        print("\nVí dụ:")
        print("  python image_resizer.py photo.jpg")
        print("  python image_resizer.py photo.jpg --no-text")
        print("  python image_resizer.py images/ --batch")
        print("  python image_resizer.py photo.jpg --output resized_photo.jpg")
        print("  python image_resizer.py images/ --batch --output resized_images/")
        return
    
    input_path = sys.argv[1]
    
    # Parse arguments
    batch_mode = '--batch' in sys.argv
    keep_aspect_ratio = '--keep-aspect' in sys.argv
    
    output_path = None
    if '--output' in sys.argv:
        output_index = sys.argv.index('--output')
        if output_index + 1 < len(sys.argv):
            output_path = sys.argv[output_index + 1]
    
    # Kiểm tra input path
    if not os.path.exists(input_path):
        print(f"❌ Đường dẫn không tồn tại: {input_path}")
        return
    
    # Xử lý theo mode
    if batch_mode or os.path.isdir(input_path):
        print("📁 Chế độ batch - Resize hàng loạt")
        resize_batch_images(input_path, output_path, keep_aspect_ratio)
    else:
        print("🖼️  Chế độ đơn lẻ - Resize 1 ảnh")
        resize_single_image(input_path, output_path, keep_aspect_ratio)

if __name__ == "__main__":
    main()

