"""
AI Service Integration for Test Case Generator
Optimized for professional test case generation with English prompts and Vietnamese output.
Supports OpenAI ChatGPT and Google Gemini APIs with streamlined processing.
"""

import os
import re
from typing import List, Dict, Any, Tuple
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False

# Vietnamese character set for language detection
VIETNAMESE_CHARS = ['ă', 'â', 'đ', 'ê', 'ô', 'ơ', 'ư', 'á', 'à', 'ả', 'ã', 'ạ',
                   'é', 'è', 'ẻ', 'ẽ', 'ẹ', 'í', 'ì', 'ỉ', 'ĩ', 'ị', 'ó', 'ò',
                   'ỏ', 'õ', 'ọ', 'ú', 'ù', 'ủ', 'ũ', 'ụ', 'ý', 'ỳ', 'ỷ', 'ỹ', 'ỵ']


class AIService:
    """
    Optimized AI service for professional test case generation.

    Features:
    - English prompts for better AI model performance
    - Vietnamese output format as specified
    - Streamlined processing without excessive post-processing
    - Flexible input validation without hardcoded keywords
    - Support for OpenAI ChatGPT and Google Gemini APIs
    """

    def __init__(self):
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        self.gemini_api_key = os.getenv('GEMINI_API_KEY')
        self.openai_model = os.getenv('OPENAI_MODEL', 'gpt-3.5-turbo')
        self.gemini_model = os.getenv('GEMINI_MODEL', 'gemini-pro')
        self.max_tokens = int(os.getenv('API_MAX_TOKENS', '4000'))
        self.timeout = int(os.getenv('API_TIMEOUT', '30'))

        # Initialize clients
        self._init_clients()

    def _init_clients(self):
        """Initialize AI clients"""
        # Initialize OpenAI
        if OPENAI_AVAILABLE and self.openai_api_key:
            openai.api_key = self.openai_api_key
            self.openai_client = openai.OpenAI(api_key=self.openai_api_key)
        else:
            self.openai_client = None

        # Initialize Gemini
        if GEMINI_AVAILABLE and self.gemini_api_key:
            genai.configure(api_key=self.gemini_api_key)
            self.gemini_client = genai.GenerativeModel(self.gemini_model)
        else:
            self.gemini_client = None
    
    def is_openai_available(self) -> bool:
        """Check if OpenAI is available and configured"""
        return OPENAI_AVAILABLE and self.openai_client is not None

    def is_gemini_available(self) -> bool:
        """Check if Gemini is available and configured"""
        return GEMINI_AVAILABLE and self.gemini_client is not None

    def get_available_models(self) -> List[str]:
        """Get list of available AI models"""
        models = []
        if self.is_openai_available():
            models.append("ChatGPT")
        if self.is_gemini_available():
            models.append("Gemini")
        return models

    def validate_and_enhance_input(self, content: str, model: str = "ChatGPT") -> Tuple[str, str]:
        """
        Validate input content and enhance if needed

        Args:
            content: Input content to validate
            model: AI model to use for validation

        Returns:
            Tuple of (status, processed_content)
            status: 'complete', 'enhanced', or 'irrelevant'
            processed_content: Original content, enhanced content, or error message
        """
        # Detect input language for consistency
        input_language = self.detect_input_language(content)

        validation_prompt = f"""
You are a strict software requirements analyst specializing in test case generation validation. Your task is to determine if the provided content contains legitimate software project requirements suitable for test case generation.

**STRICT VALIDATION CRITERIA:**
The content MUST contain ALL of the following to be considered valid:
1. **Software System Context**: Clear indication this is for a software application, system, or digital platform
2. **Functional Specifications**: Specific features, functions, or capabilities to be implemented
3. **User Interactions**: Describable user actions, workflows, or system interactions
4. **Testable Requirements**: Elements that can be verified through testing (inputs, outputs, behaviors)

**AUTOMATIC REJECTION CRITERIA - Return IRRELEVANT if content contains:**
- General business descriptions without technical specifications
- Marketing materials, promotional content, or sales documents
- Personal documents, letters, or non-technical correspondence
- Academic papers or research without implementation requirements
- News articles, blog posts, or informational content
- Financial reports, legal documents, or administrative content
- Meeting notes without specific technical requirements
- General project descriptions without functional details
- Content that is primarily narrative or descriptive without actionable requirements

**CLASSIFICATION RULES:**
1. **COMPLETE**: Content contains comprehensive software requirements including:
   - Specific software functionality descriptions
   - Clear user interaction workflows or system processes
   - Validation rules, business logic, or technical constraints
   - Expected system behaviors and responses
   - Sufficient detail for creating actionable test scenarios

2. **INCOMPLETE**: Content is clearly software-related but lacks critical testing details:
   - Software functionality is mentioned but lacks specifics
   - User workflows are vague or incomplete
   - Missing validation rules or error handling requirements
   - Technical specifications are insufficient for test creation

3. **IRRELEVANT**: Content does not contain software requirements suitable for testing:
   - Not related to software development or system implementation
   - Lacks technical specifications or functional requirements
   - Cannot generate meaningful test scenarios from the content
   - General business or non-technical documentation

**RESPONSE FORMAT:**
- If COMPLETE: Return exactly "COMPLETE"
- If INCOMPLETE: Return "INCOMPLETE\\n[enhanced content in {input_language}]"
- If IRRELEVANT: Return exactly "IRRELEVANT"

**FOR INCOMPLETE CONTENT - ENHANCEMENT GUIDELINES:**
- Add specific user interaction steps and workflows
- Include detailed validation rules and error scenarios
- Specify UI/UX elements and their expected behaviors
- Define clear input/output requirements and data formats
- Add boundary conditions and edge case scenarios
- Maintain original language ({input_language})

**CONTENT TO ANALYZE:**
{content}

**CLASSIFICATION:**"""

        try:
            if model == "ChatGPT" and self.is_openai_available():
                response = self.openai_client.chat.completions.create(
                    model=self.openai_model,
                    messages=[
                        {"role": "system", "content": "You are a professional software requirements analysis expert."},
                        {"role": "user", "content": validation_prompt}
                    ],
                    max_tokens=2000,
                    temperature=0.3,
                    timeout=self.timeout
                )
                result = response.choices[0].message.content.strip()
            elif model == "Gemini" and self.is_gemini_available():
                response = self.gemini_client.generate_content(
                    validation_prompt,
                    generation_config=genai.types.GenerationConfig(
                        max_output_tokens=2000,
                        temperature=0.3,
                    )
                )
                result = response.text.strip()


            # Parse result
            if result.startswith("COMPLETE"):
                return ("complete", content)
            elif result.startswith("INCOMPLETE"):
                enhanced_content = result.replace("INCOMPLETE\n", "").strip()
                return ("enhanced", enhanced_content if enhanced_content else content)
            elif result.startswith("IRRELEVANT"):
                return ("irrelevant", "Tài liệu không liên quan đến yêu cầu phần mềm hoặc không chứa đủ thông tin để tạo test case.")
            else:
                # Default to complete if unclear
                return ("complete", content)

        except Exception as e:
            print(f"Validation error: {str(e)}")
            # Fallback to basic validation


    def _validate_csv_format(self, csv_content: str) -> Tuple[bool, str, str]:
        """
        Validate CSV format and fix common issues

        Returns:
            Tuple of (is_valid, cleaned_csv, error_message)
        """
        if not csv_content or csv_content.strip() == "":
            return False, "", "CSV content is empty"

        lines = [line.strip() for line in csv_content.strip().split('\n') if line.strip()]
        if not lines:
            return False, "", "No valid CSV lines found"

        # Remove markdown formatting if present
        cleaned_lines = []
        for line in lines:
            # Remove markdown code block markers
            if line.startswith('```'):
                continue
            # Remove any leading/trailing whitespace
            line = line.strip()
            if line:
                cleaned_lines.append(line)

        if not cleaned_lines:
            return False, "", "No content after cleaning markdown"

        # Validate each line has exactly 5 columns
        valid_lines = []
        expected_columns = 5

        for i, line in enumerate(cleaned_lines):
            # Skip empty lines
            if not line:
                continue

            # Count columns by parsing CSV properly
            try:
                import csv
                import io
                reader = csv.reader(io.StringIO(line))
                row = next(reader)

                if len(row) == expected_columns:
                    # Check if this is a category header (empty ID field but has category name)
                    is_category_header = (not row[0].strip() and row[1].strip() and
                                        not row[2].strip() and not row[3].strip() and not row[4].strip())

                    if is_category_header:
                        # This is a category header, keep it as is
                        fixed_row = []
                        for field in row:
                            field = field.strip()
                            # Quote fields that contain commas, quotes, or line breaks
                            if ',' in field or '"' in field or '\\n' in field:
                                # Escape existing quotes
                                field = field.replace('"', '""')
                                field = f'"{field}"'
                            fixed_row.append(field)
                        valid_lines.append(','.join(fixed_row))
                    else:
                        # This is a regular test case, validate priority column
                        priority = row[-1].strip()
                        if priority and priority not in ['High', 'Normal', 'Low']:
                            # Fix invalid priority
                            if 'cao' in priority.lower() or 'high' in priority.lower():
                                row[-1] = 'High'
                            elif 'thấp' in priority.lower() or 'low' in priority.lower():
                                row[-1] = 'Low'
                            else:
                                row[-1] = 'Normal'

                        # Reconstruct line with proper quoting
                        fixed_row = []
                        for field in row:
                            field = field.strip()
                            # Quote fields that contain commas, quotes, or line breaks
                            if ',' in field or '"' in field or '\\n' in field:
                                # Escape existing quotes
                                field = field.replace('"', '""')
                                field = f'"{field}"'
                            fixed_row.append(field)

                        valid_lines.append(','.join(fixed_row))
                elif len(row) > expected_columns:
                    # Try to fix by merging overflow content back into appropriate columns
                    # This handles cases where content overflowed between columns
                    fixed_row = row[:expected_columns]

                    # If there's overflow, merge it into the expected result column (index 3)
                    if len(row) > expected_columns:
                        overflow_content = ' '.join(row[expected_columns:])
                        if len(fixed_row) > 3:
                            fixed_row[3] = f"{fixed_row[3]} {overflow_content}".strip()

                    # Fix priority
                    if len(fixed_row) >= 5:
                        priority = fixed_row[4].strip()
                        if priority not in ['High', 'Normal', 'Low']:
                            fixed_row[4] = 'Normal'

                    # Reconstruct with proper quoting
                    quoted_row = []
                    for field in fixed_row:
                        field = field.strip()
                        if ',' in field or '"' in field or '\\n' in field:
                            field = field.replace('"', '""')
                            field = f'"{field}"'
                        quoted_row.append(field)

                    valid_lines.append(','.join(quoted_row))
                else:
                    # Too few columns, pad with empty values
                    while len(row) < expected_columns:
                        row.append("")
                    valid_lines.append(','.join(row))

            except Exception as e:
                # If CSV parsing fails, try simple comma split as fallback
                parts = line.split(',')
                if len(parts) >= expected_columns:
                    # Take first 5 parts
                    parts = parts[:expected_columns]
                elif len(parts) < expected_columns:
                    # Pad with empty values
                    while len(parts) < expected_columns:
                        parts.append("")

                # Fix priority
                if len(parts) >= 5:
                    priority = parts[4].strip()
                    if priority not in ['High', 'Normal', 'Low']:
                        parts[4] = 'Normal'

                valid_lines.append(','.join(parts))

        if not valid_lines:
            return False, "", "No valid CSV lines after validation"

        cleaned_csv = '\n'.join(valid_lines)
        return True, cleaned_csv, ""


    def create_optimized_prompt(self, content: str, custom_requirements: str = None) -> str:
        """Create comprehensive English prompt for professional test case generation with intelligent multi-file handling"""

        prompt = f"""
You are a senior software testing expert. Generate comprehensive, professional test cases for the following software requirements. Focus on real-world testing scenarios that would be used in actual software projects.

**CRITICAL INSTRUCTIONS:**
- Generate test cases in Vietnamese language for all content (Mục đích kiểm thử, Các bước thực hiện, Kết quả mong muốn)
- Use ONLY the exact CSV format: ID,Mục đích kiểm thử,Các bước thực hiện,Kết quả mong muốn,Độ ưu tiên
- Do NOT add extra columns, headers, or formatting
- Each test case must be on a single line with proper CSV escaping for commas within fields
- Use priorities: High, Normal, Low (in English)

**MULTI-FILE ANALYSIS INSTRUCTIONS:**
First, analyze the provided content to determine if it contains multiple files (look for patterns like "=== File: filename ===").

**IF MULTIPLE FILES ARE DETECTED:**
1. Determine if the files are RELATED or INDEPENDENT:
   - RELATED: Files share functionality, data flow, APIs, or work together in a system
   - INDEPENDENT: Files describe completely separate requirements with no shared components

2. **For RELATED files:**
   - Create integrated test cases covering both individual file functionality AND cross-file interactions
   - Use these ID prefixes:
     * F1_, F2_, F3_ etc. for individual file test cases
     * INT_ for integration test cases between files
     * E2E_ for end-to-end workflow test cases
     * SYS_ for system-level test cases
   - Example: F1_001, F2_001, INT_001, E2E_001

3. **For INDEPENDENT files:**
   - Create separate test case sections for each file
   - Use distinct ID prefixes for each file: F1_, F2_, F3_, etc.
   - Ensure comprehensive coverage for each file independently
   - Example: F1_001, F1_002, F2_001, F2_002

**IF SINGLE FILE OR NO FILE SEPARATORS:**
- Use standard test case categories with traditional ID prefixes
- Use prefixes: TC_UI_, TC_VAL_, TC_SEARCH_, TC_FUN_, TC_ERR_, TC_PERF_

**CONTENT RELEVANCE EVALUATION:**
Carefully analyze the provided content to determine if it contains any software requirements, functional specifications, technical descriptions, or system features that can have test cases written for them.

**PROCEED WITH TEST CASE GENERATION IF:**
- Content describes software requirements, features, or functionality (even if brief or informal)
- Content contains technical specifications or system descriptions
- Content mentions user interactions, system behaviors, or functional workflows
- Content describes any software components, interfaces, or processes
- Content has ANY describable requirements or features that could reasonably be tested

**REJECT CONTENT ONLY IF:**
- Content is purely personal messages, greetings, or casual conversation with no software context
- Content is random text, meaningless strings, or placeholder text with no functional meaning
- Content is completely non-technical (recipes, stories, general topics) with no software relevance
- Content truly lacks ANY describable software requirements, features, or functionality

**FOR IRRELEVANT CONTENT:** If the content truly has no software-related requirements or functional descriptions, respond with exactly: "❌ Nội dung không liên quan đến yêu cầu phần mềm hoặc không chứa đủ thông tin để tạo test case. Vui lòng cung cấp tài liệu yêu cầu kỹ thuật hoặc mô tả chức năng phần mềm."

**REQUIREMENTS TO ANALYZE:**
{content}"""

        # Add custom requirements if provided
        if custom_requirements and custom_requirements.strip():
            prompt += f"""

**ADDITIONAL USER REQUIREMENTS:**
{custom_requirements.strip()}

IMPORTANT: Generate comprehensive test cases for ALL the original requirements above, ensuring complete functional coverage. Additionally, give special attention and enhanced coverage to the user's additional requirements listed above. The custom requirements should supplement and enhance the test coverage, not replace or reduce coverage of the base functionality."""

        prompt += """

**CRITICAL CSV FORMATTING RULES - MANDATORY COMPLIANCE:**
1. **EXACT COLUMN STRUCTURE**: Generate ONLY valid CSV format with exactly 5 columns
2. **COLUMN ORDER**: ID,Mục đích kiểm thử,Các bước thực hiện,Kết quả mong muốn,Độ ưu tiên
3. **NO COLUMN OVERFLOW**: Each column MUST contain ONLY the content specified for that column
4. **PROPER CSV ESCAPING**: Use double quotes around ANY field containing commas, line breaks, or special characters
5. **COMPLETE CONTENT**: Every test case must have full, meaningful content in ALL required fields
6. **LINE BREAK HANDLING**: Use \\n for line breaks within fields, NEVER actual line breaks
7. **VIETNAMESE TEXT ESCAPING**: Properly escape Vietnamese text containing commas or quotes

**CSV ESCAPING EXAMPLES:**
- Text with commas: "Kiểm tra validation, xác thực dữ liệu, và hiển thị lỗi"
- Text with quotes: "Hiển thị thông báo \"Đăng nhập thành công\""
- Text with line breaks: "1. Mở ứng dụng\\n2. Nhập thông tin\\n3. Nhấn nút Lưu"
- Vietnamese with commas: "Kiểm tra chức năng thêm, sửa, xóa người dùng"

**MANDATORY OUTPUT STRUCTURE WITH PERFECT CSV FORMATTING:**

**Structure Pattern:**
1. Category header row (empty fields except category name)
2. Test case rows with complete content in all fields
3. Repeat for each category

**Example 1 - Related Files (API + UI Integration):**
"","Kiểm tra chức năng API","","",""
F1_001,"Kiểm tra API tạo người dùng với dữ liệu hợp lệ","1. Chuẩn bị dữ liệu test hợp lệ\\n2. Gửi POST request đến /api/users\\n3. Kiểm tra response status code 201\\n4. Xác nhận user object được trả về\\n5. Kiểm tra user được lưu vào database","API trả về user object với ID hợp lệ, status 201, và user được tạo thành công trong database",High
F1_002,"Kiểm tra API validation với email trùng lặp","1. Tạo user vớ<NAME_EMAIL>\\n2. Gửi POST request với cùng email\\n3. Kiểm tra response status 400\\n4. Xác nhận error message","API trả về status 400 với message \"Email đã tồn tại trong hệ thống\"",High
"","Kiểm tra giao diện người dùng","","",""
F2_001,"Kiểm tra form thêm người dùng với dữ liệu hợp lệ","1. Mở trang quản lý user\\n2. Nhấn nút \"Thêm người dùng\"\\n3. Nhập đầy đủ thông tin hợp lệ\\n4. Nhấn nút \"Lưu\"\\n5. Kiểm tra thông báo thành công","Form hiển thị thông báo \"Thêm người dùng thành công\" và chuyển về danh sách",High
"","Kiểm tra tích hợp API-UI","","",""
INT_001,"Kiểm tra luồng tạo user từ UI đến API","1. Mở form thêm user trên UI\\n2. Nhập thông tin: Tên, Email, Password\\n3. Nhấn \"Lưu\" và theo dõi network request\\n4. Xác nhận API call được gửi đúng\\n5. Kiểm tra UI cập nhật sau khi API response","User mới xuất hiện trong danh sách với thông tin chính xác, API được gọi thành công",High

**Example 2 - Independent Files:**
"","File 1: Chức năng đăng nhập","","",""
F1_001,"Kiểm tra đăng nhập với thông tin hợp lệ","1. Mở trang đăng nhập\\n2. Nhập username và password đúng\\n3. Nhấn nút \"Đăng nhập\"\\n4. Kiểm tra chuyển hướng","Hệ thống chuyển hướng đến trang chính và hiển thị thông tin user",High
"","File 2: Chức năng báo cáo","","",""
F2_001,"Kiểm tra tạo báo cáo theo tháng","1. Vào module báo cáo\\n2. Chọn loại \"Báo cáo tháng\"\\n3. Chọn tháng và năm\\n4. Nhấn \"Tạo báo cáo\"","Báo cáo được tạo thành công và hiển thị dữ liệu chính xác",Normal

**Example 3 - Single File (Traditional Categories):**
"","Kiểm tra giao diện người dùng","","",""
TC_UI_001,"Kiểm tra hiển thị màn hình đăng nhập","1. Mở ứng dụng trong trình duyệt\\n2. Điều hướng đến URL đăng nhập\\n3. Kiểm tra các thành phần giao diện\\n4. Xác nhận responsive design","Màn hình đăng nhập hiển thị đầy đủ với logo, form đăng nhập, và footer",High
"","Kiểm tra validate dữ liệu đầu vào","","",""
TC_VAL_001,"Kiểm tra validation khi để trống email","1. Mở trang đăng nhập\\n2. Để trống trường email\\n3. Nhập password hợp lệ\\n4. Nhấn nút \"Đăng nhập\"","Hiển thị thông báo lỗi \"Vui lòng nhập địa chỉ email\" màu đỏ dưới trường email",High

**MANDATORY CSV FORMATTING COMPLIANCE:**

**CRITICAL OUTPUT RULES:**
1. **PURE CSV ONLY**: Return ONLY CSV content - no markdown, no explanations, no extra text
2. **EXACT 5 COLUMNS**: Every row must have exactly 5 comma-separated values
3. **PROPER ESCAPING**: Use double quotes for ANY field containing commas, quotes, or special characters
4. **COMPLETE FIELDS**: NO empty or truncated content - every field must be meaningful and complete
5. **CATEGORY HEADERS**: Use format: "","Category Name in Vietnamese","","",""
6. **TEST CASE FORMAT**: Use format: ID,"Purpose","Steps","Expected","Priority"

**FIELD CONTENT REQUIREMENTS:**
- **ID Field**: Test case ID only (TC_001, F1_001, INT_001, etc.) OR empty for headers
- **Purpose Field**: Complete test objective in Vietnamese (50-100 characters)
- **Steps Field**: Detailed numbered steps with \\n separators (complete workflow)
- **Expected Field**: Specific, measurable expected result in Vietnamese
- **Priority Field**: High, Normal, or Low (English) OR empty for headers

**VIETNAMESE TEXT HANDLING:**
- Properly escape commas: "Kiểm tra thêm, sửa, xóa dữ liệu"
- Escape quotes: "Hiển thị \"Thành công\""
- Use \\n for line breaks: "1. Bước 1\\n2. Bước 2\\n3. Bước 3"
- Maintain Vietnamese diacritics correctly

**QUALITY ASSURANCE CHECKLIST:**
✓ Each test case is independently executable
✓ Test steps are clear, detailed, and actionable
✓ Expected results are specific and verifiable
✓ Both positive and negative scenarios included
✓ Realistic data and user behaviors incorporated
✓ Complete coverage of all mentioned functionality
✓ Perfect CSV formatting with proper escaping
✓ All fields contain complete, meaningful content

**FINAL VALIDATION:**
Before generating output, verify:
- Every row has exactly 5 columns
- All Vietnamese text with commas is properly quoted
- Category headers follow exact format
- Test cases have complete content in all fields
- No content spills between columns

**COMPREHENSIVE COVERAGE MANDATE:**
Generate complete and thorough test cases covering ALL functionality mentioned in the requirements. Do not skip any features or functions. Ensure every requirement has appropriate test coverage across multiple categories. If additional user requirements are provided, enhance the coverage for those areas while maintaining full coverage of all base requirements.

**BEGIN CSV OUTPUT NOW (NO ADDITIONAL TEXT):**"""

        return prompt



    def _generate_with_ai(self, content: str, model: str, custom_requirements: str = None) -> str:
        """Unified AI generation method"""
        prompt = self.create_optimized_prompt(content, custom_requirements)

        try:
            if model == "ChatGPT" and self.is_openai_available():
                response = self.openai_client.chat.completions.create(
                    model=self.openai_model,
                    messages=[
                        {"role": "system", "content": "You are a professional software testing expert specializing in comprehensive test case generation."},
                        {"role": "user", "content": prompt}
                    ],
                    max_tokens=self.max_tokens,
                    temperature=0.2,
                    timeout=self.timeout
                )
                return response.choices[0].message.content.strip()

            elif model == "Gemini" and self.is_gemini_available():
                response = self.gemini_client.generate_content(
                    prompt,
                    generation_config=genai.types.GenerationConfig(
                        max_output_tokens=self.max_tokens,
                        temperature=0.2,
                    )
                )
                return response.text.strip()
            else:
                raise ValueError(f"Model {model} is not available or not configured")

        except Exception as e:
            raise Exception(f"AI generation error ({model}): {str(e)}")


    def generate_test_cases(self, content: str, model: str = "ChatGPT", custom_requirements: str = None) -> str:
        """
        Generate test cases with input validation and enhancement

        Args:
            content: Functional requirements content
            model: AI model to use ("ChatGPT" or "Gemini")
            custom_requirements: Additional user requirements or instructions

        Returns:
            Generated test cases as CSV string or error message
        """
        # Step 1: Validate and enhance input
        # status, processed_content = self.validate_and_enhance_input(content, model)
        #
        # # Step 2: Handle different validation results
        # if status == "irrelevant":
        #     return processed_content  # Return Vietnamese error message

        # Step 3: Generate test cases with processed content
        processed_content = content
        final_content = processed_content
        # if status == "enhanced":
        #     # Add note about enhancement for transparency
        #     final_content = f"[Nội dung đã được bổ sung chi tiết]\n\n{processed_content}"

        # Generate with AI - optimized prompts should produce clean output
        raw_result = self._generate_with_ai(final_content, model, custom_requirements)

        # Validate and clean CSV format to ensure column integrity
        is_valid, cleaned_csv, error_message = self._validate_csv_format(raw_result)

        if not is_valid:
            return f"❌ Lỗi định dạng CSV: {error_message}. Vui lòng thử lại hoặc cung cấp tài liệu yêu cầu chi tiết hơn."

        return cleaned_csv



    def parse_csv_response_working(self, csv_content: str) -> List[Dict[str, str]]:
        """Working CSV parsing that handles commas in expected result field and category headers"""
        if not csv_content or csv_content.strip() == "Yêu cầu mô tả rõ lại nội dung dự án":
            return []

        lines = [line.strip() for line in csv_content.strip().split('\n') if line.strip()]
        if not lines:
            return []

        # Find header line (skip if present)
        start_idx = 0
        for i, line in enumerate(lines):
            if 'ID,' in line and 'Mục đích kiểm thử' in line:
                start_idx = i + 1
                break

        headers = ['ID', 'Mục đích kiểm thử', 'Các bước thực hiện', 'Kết quả mong muốn', 'Độ ưu tiên']
        test_cases = []

        # Process data lines
        for line in lines[start_idx:]:
            if not line:
                continue

            # Check if this is a category header (empty ID, category name in second column, other columns empty)
            temp_parts = self._split_csv_line(line)
            if (len(temp_parts) >= 2 and
                not temp_parts[0].strip() and  # Empty ID
                temp_parts[1].strip() and      # Has category name
                (len(temp_parts) < 3 or not temp_parts[2].strip()) and  # Empty steps
                (len(temp_parts) < 4 or not temp_parts[3].strip()) and  # Empty expected result
                (len(temp_parts) < 5 or not temp_parts[4].strip())):    # Empty priority

                # This is a category header
                category_header = {
                    'ID': '',
                    'Mục đích kiểm thử': temp_parts[1].strip(),
                    'Các bước thực hiện': '',
                    'Kết quả mong muốn': '',
                    'Độ ưu tiên': ''
                }
                test_cases.append(category_header)
                continue

            # Skip lines that don't have enough commas for test cases
            if line.count(',') < 2:
                continue

            # Strategy: Find the priority at the end using regex, then work backwards
            valid_priorities = ['High', 'Normal', 'Low']
            parts = None

            # Try to find a valid priority at the end of the line
            for priority in valid_priorities:
                # Look for priority at the end, possibly with trailing whitespace
                pattern = rf',\s*{priority}\s*$'
                match = re.search(pattern, line, re.IGNORECASE)

                if match:
                    # Found valid priority, extract everything before it
                    before_priority = line[:match.start()]
                    priority_value = priority  # Use the standardized case

                    # Split the part before priority using standard CSV parsing
                    temp_parts = self._split_csv_line(before_priority)

                    # We should have exactly 4 parts: ID, Purpose, Steps, Expected
                    if len(temp_parts) == 4:
                        # Perfect case
                        temp_parts.append(priority_value)
                        parts = temp_parts
                        break
                    elif len(temp_parts) > 4:
                        # Too many parts due to commas in expected result
                        # Keep first 3 parts (ID, Purpose, Steps) and merge the rest
                        fixed_parts = temp_parts[:3]
                        expected_result = ', '.join(temp_parts[3:])  # Merge with proper spacing
                        fixed_parts.append(expected_result)
                        fixed_parts.append(priority_value)
                        parts = fixed_parts
                        break
                    elif len(temp_parts) < 4:
                        # Too few parts, pad with empty strings
                        while len(temp_parts) < 4:
                            temp_parts.append("")
                        temp_parts.append(priority_value)
                        parts = temp_parts
                        break

            # If no valid priority found at the end, use standard parsing
            if parts is None:
                parts = self._split_csv_line(line)

                # If we have more than 5 parts, try to fix by merging middle parts
                if len(parts) > 5:
                    # Assume last part is priority (might be invalid)
                    # Merge parts 3 to second-to-last into expected result
                    fixed_parts = parts[:3]  # ID, Purpose, Steps
                    expected_result = ', '.join(parts[3:-1])  # Merge middle parts
                    priority = parts[-1] if parts[-1].strip() else 'Normal'

                    # Validate and fix priority
                    if priority.strip().title() not in valid_priorities:
                        priority = 'Normal'

                    fixed_parts.extend([expected_result, priority])
                    parts = fixed_parts

                # Validate priority if we have enough parts
                elif len(parts) >= 5:
                    priority = parts[-1].strip().title()
                    if priority not in valid_priorities:
                        parts[-1] = 'Normal'

            # Ensure we have exactly 5 parts
            while len(parts) < 5:
                parts.append("")
            if len(parts) > 5:
                parts = parts[:5]

            # Create test case dictionary
            test_case = {header: parts[i].strip() if i < len(parts) else ""
                        for i, header in enumerate(headers)}

            test_cases.append(test_case)

        return test_cases

    def parse_csv_response(self, csv_content: str) -> List[Dict[str, str]]:
        """Simplified CSV parsing with better error handling"""
        # Use the working method for now
        return self.parse_csv_response_working(csv_content)

    def _split_csv_line(self, line: str) -> List[str]:
        """Standard CSV line splitting with quote handling"""
        parts = []
        current_part = ""
        in_quotes = False

        for char in line:
            if char == '"':
                in_quotes = not in_quotes
            elif char == ',' and not in_quotes:
                parts.append(current_part.strip('"').strip())
                current_part = ""
            else:
                current_part += char

        parts.append(current_part.strip('"').strip())
        return parts

    def validate_vietnamese_response(self, response: str) -> bool:
        """Validate that response contains Vietnamese content using character analysis"""
        return any(char in response for char in VIETNAMESE_CHARS)

    def detect_input_language(self, content: str) -> str:
        """Detect if input is primarily Vietnamese or English using character analysis"""
        vietnamese_char_count = sum(1 for char in content if char in VIETNAMESE_CHARS)

        # If Vietnamese characters are present, likely Vietnamese
        if vietnamese_char_count > 0:
            return 'vietnamese'

        # Default to English if no Vietnamese characters detected
        return 'english'

    def get_service_status(self) -> Dict[str, Any]:
        """Get comprehensive status of AI services"""
        return {
            'openai': {
                'available': self.is_openai_available(),
                'model': self.openai_model if self.is_openai_available() else None,
                'configured': bool(self.openai_api_key)
            },
            'gemini': {
                'available': self.is_gemini_available(),
                'model': self.gemini_model if self.is_gemini_available() else None,
                'configured': bool(self.gemini_api_key)
            },
            'features': {
                'input_validation': True,
                'content_enhancement': True,
                'language_consistency': True,
                'vietnamese_support': True
            }
        }
