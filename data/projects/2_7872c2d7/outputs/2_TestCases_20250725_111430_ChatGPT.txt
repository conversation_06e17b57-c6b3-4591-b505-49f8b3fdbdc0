================================================================================
TEST CASES - 2
Generated on: 2025-07-25 11:14:30
================================================================================

Test Case #1
----------------------------------------
ID: ### Kiểm tra giao diện người dùng (UI Testing)
Mục đích kiểm thử: 
Độ ưu tiên: Normal

Các bước thực hiện:

Kết quả mong muốn:

--------------------------------------------------------------------------------


============================================================
KIỂM TRA GIAO DIỆN NGƯỜI DÙNG
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_001
Mục đích kiểm thử: Kiểm tra hiển thị màn hình danh sách thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở ứng dụng
  2. Điều hướng đến màn hình danh sách thử việc
  3. Kiểm tra hiển thị các trường thông tin

Kết quả mong muốn:
  Màn hình danh sách thử việc hiển thị đúng các thông tin nhân viên cần thiết

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_UI_002
Mục đích kiểm thử: Kiểm tra chức năng tạo mới thông tin thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Mở ứng dụng
  2. Điều hướng đến màn hình tạo mới thông tin thử việc
  3. Kiểm tra hiển thị các trường nhập thông tin

Kết quả mong muốn:
  Màn hình tạo mới thông tin thử việc hiển thị đầy đủ các trường nhập thông tin

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: ### Kiểm tra validate dữ liệu đầu vào (Input Validation Testing)
Mục đích kiểm thử: 
Độ ưu tiên: Normal

Các bước thực hiện:

Kết quả mong muốn:

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE DỮ LIỆU ĐẦU VÀO
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_001
Mục đích kiểm thử: Kiểm tra validation khi để trống tên nhân viên
Độ ưu tiên: High

Các bước thực hiện:
  1. Nhập thông tin thử việc với tên nhân viên trống
  2. Nhập các thông tin khác
  3. Lưu thông tin

Kết quả mong muốn:
  Hệ thống hiển thị thông báo lỗi khi tên nhân viên trống

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_002
Mục đích kiểm thử: Kiểm tra validation ngày bắt đầu thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Nhập thông tin thử việc với ngày bắt đầu không hợp lệ
  2. Nhập các thông tin khác
  3. Lưu thông tin

Kết quả mong muốn:
  Hệ thống cảnh báo lỗi khi ngày bắt đầu không hợp lệ

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: ### Kiểm tra validate trường tìm kiếm (Search Field Validation)
Mục đích kiểm thử: 
Độ ưu tiên: Normal

Các bước thực hiện:

Kết quả mong muốn:

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TRƯỜNG TÌM KIẾM
============================================================

Test Case #1
----------------------------------------
ID: TC_SEARCH_001
Mục đích kiểm thử: Kiểm tra validation khi nhập tên nhân viên vào ô tìm kiếm
Độ ưu tiên: High

Các bước thực hiện:
  1. Nhập tên nhân viên vào ô tìm kiếm
  2. Bấm nút tìm kiếm
  3. Kiểm tra kết quả hiển thị

Kết quả mong muốn:
  Hệ thống hiển thị danh sách nhân viên phù hợp với tên đã nhập

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: ### Kiểm tra chức năng (Functional Testing)
Mục đích kiểm thử: 
Độ ưu tiên: Normal

Các bước thực hiện:

Kết quả mong muốn:

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_001
Mục đích kiểm thử: Kiểm tra chức năng chỉnh sửa thông tin thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn nhân viên cần chỉnh sửa
  2. Click vào nút chỉnh sửa
  3. Thay đổi thông tin
  4. Lưu cập nhật

Kết quả mong muốn:
  Hệ thống cập nhật thông tin thử việc của nhân viên thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_FUN_002
Mục đích kiểm thử: Kiểm tra chức năng xóa giai đoạn thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Chọn giai đoạn cần xóa
  2. Click vào nút xóa
  3. Xác nhận xóa
  4. Kiểm tra giai đoạn đã bị xóa

Kết quả mong muốn:
  Hệ thống xóa giai đoạn thử việc thành công

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: ### Kiểm tra xử lý lỗi (Error Handling Testing)
Mục đích kiểm thử: 
Độ ưu tiên: Normal

Các bước thực hiện:

Kết quả mong muốn:

--------------------------------------------------------------------------------



============================================================
KIỂM TRA XỬ LÝ LỖI
============================================================

Test Case #1
----------------------------------------
ID: TC_ERR_001
Mục đích kiểm thử: Kiểm tra xử lý lỗi khi lưu thông tin thử việc thất bại
Độ ưu tiên: High

Các bước thực hiện:
  1. Nhập thông tin thử việc không hợp lệ
  2. Lưu thông tin
  3. Kiểm tra thông báo lỗi

Kết quả mong muốn:
  Hệ thống hiển thị thông báo lỗi khi lưu thông tin thử việc không thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: ### Kiểm tra hiệu năng (Performance Testing)
Mục đích kiểm thử: 
Độ ưu tiên: Normal

Các bước thực hiện:

Kết quả mong muốn:

--------------------------------------------------------------------------------



============================================================
KIỂM TRA HIỆU NĂNG
============================================================

Test Case #1
----------------------------------------
ID: TC_PERF_001
Mục đích kiểm thử: Kiểm tra thời gian load danh sách thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở ứng dụng
  2. Truy cập vào danh sách thử việc
  3. Đo thời gian load danh sách

Kết quả mong muốn:
  Hệ thống hiển thị danh sách thử việc trong thời gian chấp nhận được

--------------------------------------------------------------------------------


Total Test Cases Generated: 15
Generated by Test Case Generator v1.0
