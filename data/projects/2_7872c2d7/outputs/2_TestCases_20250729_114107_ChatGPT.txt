================================================================================
TEST CASES - 2
Generated on: 2025-07-29 11:41:07
================================================================================

Test Case #1
----------------------------------------
ID: ### Kiểm tra giao diện người dùng (UI Testing)
Mục đích kiểm thử: 
Độ ưu tiên: Normal

Các bước thực hiện:

Kết quả mong muốn:

--------------------------------------------------------------------------------


============================================================
KIỂM TRA GIAO DIỆN NGƯỜI DÙNG
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_001
Mục đích kiểm thử: Kiểm tra màn hình danh sách thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở ứng dụng
  2. Điều hướng đến màn hình danh sách thử việc
  3. Kiểm tra hiển thị các trường thông tin

Kết quả mong muốn:
  Hiển thị danh sách thử việc đầy đủ và chính xác

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_UI_002
Mục đích kiểm thử: Kiểm tra màn hình tạo thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Mở ứng dụng
  2. Điều hướng đến màn hình tạo thử việc
  3. Kiểm tra giao diện nhập thông tin thử việc

Kết quả mong muốn:
  Giao diện tạo thử việc hiển thị đúng các trường thông tin cần thiết

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: ### Kiểm tra validate dữ liệu đầu vào (Input Validation Testing)
Mục đích kiểm thử: 
Độ ưu tiên: Normal

Các bước thực hiện:

Kết quả mong muốn:

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE DỮ LIỆU ĐẦU VÀO
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_001
Mục đích kiểm thử: Kiểm tra validation khi để trống tên nhân viên
Độ ưu tiên: High

Các bước thực hiện:
  1. Nhập thông tin thử việc với tên nhân viên trống
  2. Nhập các thông tin khác
  3. Lưu thông tin

Kết quả mong muốn:
  Hiển thị thông báo lỗi khi tên nhân viên trống

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_002
Mục đích kiểm thử: Kiểm tra validation ngày bắt đầu thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Nhập ngày bắt đầu sau ngày kết thúc
  2. Lưu thông tin

Kết quả mong muốn:
  Hiển thị thông báo lỗi về ngày bắt đầu không hợp lệ

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: ### Kiểm tra validate trường tìm kiếm (Search Field Validation)
Mục đích kiểm thử: 
Độ ưu tiên: Normal

Các bước thực hiện:

Kết quả mong muốn:

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TRƯỜNG TÌM KIẾM
============================================================

Test Case #1
----------------------------------------
ID: TC_SEARCH_001
Mục đích kiểm thử: Kiểm tra tìm kiếm theo tên nhân viên
Độ ưu tiên: High

Các bước thực hiện:
  1. Nhập tên nhân viên vào ô tìm kiếm
  2. Bấm nút tìm kiếm
  3. Kiểm tra kết quả hiển thị

Kết quả mong muốn:
  Hiển thị danh sách nhân viên phù hợp với tên tìm kiếm

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: ### Kiểm tra chức năng (Functional Testing)
Mục đích kiểm thử: 
Độ ưu tiên: Normal

Các bước thực hiện:

Kết quả mong muốn:

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_001
Mục đích kiểm thử: Kiểm tra chức năng chỉnh sửa thông tin thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn nhân viên cần chỉnh sửa
  2. Click vào nút chỉnh sửa
  3. Thay đổi thông tin
  4. Lưu cập nhật

Kết quả mong muốn:
  Thông tin thử việc được cập nhật thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_FUN_002
Mục đích kiểm thử: Kiểm tra chức năng thêm giai đoạn thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Chọn nhân viên cần thêm giai đoạn
  2. Click vào nút thêm giai đoạn
  3. Nhập thông tin giai đoạn
  4. Lưu thông tin

Kết quả mong muốn:
  Giai đoạn thử việc được thêm vào thành công

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: ### Kiểm tra xử lý lỗi (Error Handling Testing)
Mục đích kiểm thử: 
Độ ưu tiên: Normal

Các bước thực hiện:

Kết quả mong muốn:

--------------------------------------------------------------------------------



============================================================
KIỂM TRA XỬ LÝ LỖI
============================================================

Test Case #1
----------------------------------------
ID: TC_ERR_001
Mục đích kiểm thử: Kiểm tra xử lý lỗi khi ngày kết thúc trước ngày bắt đầu
Độ ưu tiên: High

Các bước thực hiện:
  1. Nhập ngày kết thúc trước ngày bắt đầu
  2. Lưu thông tin

Kết quả mong muốn:
  Hiển thị thông báo lỗi về ngày kết thúc không hợp lệ

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: ### Kiểm tra hiệu năng (Performance Testing)
Mục đích kiểm thử: 
Độ ưu tiên: Normal

Các bước thực hiện:

Kết quả mong muốn:

--------------------------------------------------------------------------------



============================================================
KIỂM TRA HIỆU NĂNG
============================================================

Test Case #1
----------------------------------------
ID: TC_PERF_001
Mục đích kiểm thử: Kiểm tra thời gian load danh sách thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở ứng dụng
  2. Truy cập vào danh sách thử việc
  3. Đo thời gian load

Kết quả mong muốn:
  Thời gian load danh sách thử việc nhanh chóng và ổn định

--------------------------------------------------------------------------------


Total Test Cases Generated: 15
Generated by Test Case Generator v1.0
