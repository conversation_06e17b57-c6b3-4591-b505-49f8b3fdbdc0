================================================================================
TEST CASES - 2
Generated on: 2025-07-29 16:56:38
================================================================================


============================================================
MÀN HÌNH DANH SÁCH THỬ VIỆC
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_001
Mục đích kiểm thử: Kiểm tra hiển thị màn hình danh sách thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập vào trang danh sách thử việc

Kết quả mong muốn:
  Màn hình hiển thị danh sách thử việc với các cột: Tên nhân viên, Phòng ban, Vị trí, Hình thức, Ngày bắt đầu, Ngày kết thúc, Button Chỉnh sửa

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_UI_002
Mục đích kiểm thử: Kiểm tra trạng thái ban đầu của các button
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập vào trang danh sách thử việc

Kết quả mong muốn:
  Các button (nếu có) đều enable trừ button Lưu/Lưu cập nhật disable

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_SEARCH_001
Mục đích kiểm thử: Kiểm tra chức năng lọc theo vị trí
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Nhập vị trí vào ô input tìm kiếm
  2. Nhấn Enter hoặc click button tìm kiếm

Kết quả mong muốn:
  Danh sách hiển thị các nhân viên có vị trí trùng khớp với từ khóa tìm kiếm

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: TC_SEARCH_002
Mục đích kiểm thử: Kiểm tra chức năng lọc theo phòng ban
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Nhập phòng ban vào ô input tìm kiếm
  2. Nhấn Enter hoặc click button tìm kiếm

Kết quả mong muốn:
  Danh sách hiển thị các nhân viên thuộc phòng ban trùng khớp với từ khóa tìm kiếm

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: TC_FUN_001
Mục đích kiểm thử: Kiểm tra hiển thị tên nhân viên
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Truy cập vào trang danh sách thử việc

Kết quả mong muốn:
  Hiển thị tên nhân viên thử việc

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: TC_FUN_002
Mục đích kiểm thử: Kiểm tra hiển thị phòng ban của nhân viên
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Truy cập vào trang danh sách thử việc

Kết quả mong muốn:
  Hiển thị phòng ban của nhân viên thử việc

--------------------------------------------------------------------------------

Test Case #7
----------------------------------------
ID: TC_FUN_003
Mục đích kiểm thử: Kiểm tra hiển thị vị trí của nhân viên
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Truy cập vào trang danh sách thử việc

Kết quả mong muốn:
  Hiển thị vị trí của nhân viên thử việc

--------------------------------------------------------------------------------

Test Case #8
----------------------------------------
ID: TC_FUN_004
Mục đích kiểm thử: Kiểm tra hiển thị hình thức làm việc của nhân viên
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Truy cập vào trang danh sách thử việc

Kết quả mong muốn:
  Hiển thị hình thức làm việc của nhân viên thử việc

--------------------------------------------------------------------------------

Test Case #9
----------------------------------------
ID: TC_FUN_005
Mục đích kiểm thử: Kiểm tra hiển thị ngày bắt đầu thử việc của nhân viên
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Truy cập vào trang danh sách thử việc

Kết quả mong muốn:
  Hiển thị ngày bắt đầu thử việc của nhân viên thử việc

--------------------------------------------------------------------------------

Test Case #10
----------------------------------------
ID: TC_FUN_006
Mục đích kiểm thử: Kiểm tra hiển thị ngày kết thúc thử việc của nhân viên
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Truy cập vào trang danh sách thử việc

Kết quả mong muốn:
  Hiển thị ngày kết thúc thử việc của nhân viên thử việc

--------------------------------------------------------------------------------

Test Case #11
----------------------------------------
ID: TC_FUN_007
Mục đích kiểm thử: Kiểm tra điều hướng đến màn chỉnh sửa thông tin thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Click vào button Chỉnh sửa của một nhân viên

Kết quả mong muốn:
  Điều hướng đến màn chỉnh sửa thông tin thử việc của nhân viên đó

--------------------------------------------------------------------------------

Test Case #12
----------------------------------------
ID: TC_FUN_008
Mục đích kiểm thử: Kiểm tra khi click tên nhân viên đã có bản ghi thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Click vào tên nhân viên đã có bản ghi thử việc

Kết quả mong muốn:
  Chuyển đến màn Thông tin thử việc

--------------------------------------------------------------------------------

Test Case #13
----------------------------------------
ID: TC_FUN_009
Mục đích kiểm thử: Kiểm tra khi click tên nhân viên chưa có bản ghi thử việc - hiển thị popup
Độ ưu tiên: High

Các bước thực hiện:
  1. Click vào tên nhân viên chưa có bản ghi thử việc

Kết quả mong muốn:
  Hiện popup \Bạn có muốn tạo Thông tin Thử việc\

--------------------------------------------------------------------------------

Test Case #14
----------------------------------------
ID: TC_FUN_010
Mục đích kiểm thử: Kiểm tra khi click tên nhân viên chưa có bản ghi thử việc - click button Có
Độ ưu tiên: High

Các bước thực hiện:
  1. Click vào tên nhân viên chưa có bản ghi thử việc
  2. Click Button Có trên popup

Kết quả mong muốn:
  Chuyển đến màn tạo Thông tin thử việc

--------------------------------------------------------------------------------

Test Case #15
----------------------------------------
ID: TC_FUN_011
Mục đích kiểm thử: Kiểm tra khi click tên nhân viên chưa có bản ghi thử việc - click button Hủy
Độ ưu tiên: High

Các bước thực hiện:
  1. Click vào tên nhân viên chưa có bản ghi thử việc
  2. Click Button Hủy trên popup

Kết quả mong muốn:
  Đóng popup

--------------------------------------------------------------------------------



============================================================
MÀN HÌNH TẠO THỬ VIỆC
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_012
Mục đích kiểm thử: Kiểm tra hiển thị màn hình tạo thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập vào trang tạo thông tin thử việc

Kết quả mong muốn:
  Màn hình hiển thị form tạo thông tin thử việc với các trường: Thông tin nhân sự thử việc, Chọn quản lý trực tiếp, Chọn người theo dõi, Ngày bắt đầu thử việc, Ngày kết thúc thử việc, Btn Thêm giai đoạn, Btn Xóa giai đoạn, Chọn người đánh giá, Chọn mẫu đánh giá, Cài đặt trọng số chấm điểm, Input nhập trọng số, Hạn đánh giá, Hạn phê duyệt, Chọn kết quả mặc định, Gửi thông báo nhắc nhở đánh giá trước, Btn Lưu, Btn Hủy

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_UI_013
Mục đích kiểm thử: Kiểm tra trạng thái ban đầu của các button trên màn tạo thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập vào trang tạo thông tin thử việc

Kết quả mong muốn:
  Các button đều enable

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_VAL_001
Mục đích kiểm thử: Kiểm tra validation trường Chọn quản lý trực tiếp
Độ ưu tiên: High

Các bước thực hiện:
  1. Để trống trường Chọn quản lý trực tiếp
  2. Nhấn nút Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi \Vui lòng chọn quản lý trực tiếp\

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: TC_VAL_002
Mục đích kiểm thử: Kiểm tra validation trường Ngày bắt đầu thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Để trống trường Ngày bắt đầu thử việc
  2. Nhấn nút Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi \Vui lòng nhập ngày bắt đầu thử việc\

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: TC_VAL_003
Mục đích kiểm thử: Kiểm tra validation trường Ngày kết thúc thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Để trống trường Ngày kết thúc thử việc
  2. Nhấn nút Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi \Vui lòng nhập ngày kết thúc thử việc\

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: TC_FUN_014
Mục đích kiểm thử: Kiểm tra chức năng chọn quản lý trực tiếp
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click vào ô input Chọn quản lý trực tiếp
  2. Chọn một quản lý từ danh sách

Kết quả mong muốn:
  Quản lý được chọn hiển thị trong ô input

--------------------------------------------------------------------------------

Test Case #7
----------------------------------------
ID: TC_FUN_015
Mục đích kiểm thử: Kiểm tra chức năng chọn người theo dõi
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click vào ô input Chọn người theo dõi
  2. Chọn nhiều người theo dõi từ danh sách

Kết quả mong muốn:
  Những người theo dõi được chọn hiển thị trong ô input

--------------------------------------------------------------------------------

Test Case #8
----------------------------------------
ID: TC_FUN_016
Mục đích kiểm thử: Kiểm tra chức năng nhập ngày bắt đầu thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click vào ô input Ngày bắt đầu thử việc
  2. Chọn một ngày từ calendar

Kết quả mong muốn:
  Ngày được chọn hiển thị trong ô input

--------------------------------------------------------------------------------

Test Case #9
----------------------------------------
ID: TC_FUN_017
Mục đích kiểm thử: Kiểm tra chức năng nhập ngày kết thúc thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click vào ô input Ngày kết thúc thử việc
  2. Chọn một ngày từ calendar

Kết quả mong muốn:
  Ngày được chọn hiển thị trong ô input

--------------------------------------------------------------------------------

Test Case #10
----------------------------------------
ID: TC_FUN_018
Mục đích kiểm thử: Kiểm tra chức năng thêm giai đoạn thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Nhập tên giai đoạn vào ô input
  2. Nhấn btn Thêm giai đoạn

Kết quả mong muốn:
  Giai đoạn mới được hiển thị ở phía dưới

--------------------------------------------------------------------------------

Test Case #11
----------------------------------------
ID: TC_FUN_019
Mục đích kiểm thử: Kiểm tra chức năng xóa giai đoạn thử việc
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click btn Xóa của một giai đoạn

Kết quả mong muốn:
  Giai đoạn tương ứng bị xóa

--------------------------------------------------------------------------------

Test Case #12
----------------------------------------
ID: TC_FUN_020
Mục đích kiểm thử: Kiểm tra chức năng chọn người đánh giá cho giai đoạn
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click vào ô input Chọn người đánh giá trong giai đoạn
  2. Chọn nhiều người đánh giá từ danh sách

Kết quả mong muốn:
  Những người đánh giá được chọn hiển thị trong ô input

--------------------------------------------------------------------------------

Test Case #13
----------------------------------------
ID: TC_FUN_021
Mục đích kiểm thử: Kiểm tra chức năng chọn mẫu đánh giá cho từng người đánh giá
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click vào ô input Chọn mẫu đánh giá cho từng người đánh giá
  2. Chọn một mẫu đánh giá từ danh sách

Kết quả mong muốn:
  Mẫu đánh giá được chọn hiển thị trong ô input

--------------------------------------------------------------------------------

Test Case #14
----------------------------------------
ID: TC_FUN_022
Mục đích kiểm thử: Kiểm tra chức năng hiển thị form Cài đặt trọng số chấm điểm
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Ấn btn Cài đặt trọng số chấm điểm

Kết quả mong muốn:
  Hiển thị form Cài đặt trọng số chấm điểm

--------------------------------------------------------------------------------

Test Case #15
----------------------------------------
ID: TC_FUN_023
Mục đích kiểm thử: Kiểm tra chức năng nhập trọng số cho các tiêu chí
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Nhập trọng số vào ô input nhập trọng số cho các tiêu chí

Kết quả mong muốn:
  Trọng số được nhập hiển thị trong ô input

--------------------------------------------------------------------------------

Test Case #16
----------------------------------------
ID: TC_FUN_024
Mục đích kiểm thử: Kiểm tra chức năng nhập hạn đánh giá
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click vào ô input Hạn đánh giá
  2. Chọn một ngày từ calendar

Kết quả mong muốn:
  Ngày được chọn hiển thị trong ô input

--------------------------------------------------------------------------------

Test Case #17
----------------------------------------
ID: TC_FUN_025
Mục đích kiểm thử: Kiểm tra chức năng nhập hạn phê duyệt
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click vào ô input Hạn phê duyệt
  2. Chọn một ngày từ calendar

Kết quả mong muốn:
  Ngày được chọn hiển thị trong ô input

--------------------------------------------------------------------------------

Test Case #18
----------------------------------------
ID: TC_FUN_026
Mục đích kiểm thử: Kiểm tra chức năng chọn kết quả mặc định
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click vào ô input Chọn kết quả mặc định
  2. Chọn một kết quả từ danh sách

Kết quả mong muốn:
  Kết quả được chọn hiển thị trong ô input

--------------------------------------------------------------------------------

Test Case #19
----------------------------------------
ID: TC_FUN_027
Mục đích kiểm thử: Kiểm tra chức năng chọn số ngày gửi thông báo nhắc nhở đánh giá trước
Độ ưu tiên: Normal

Các bước thực hiện:
  1. Click vào ô input Gửi thông báo nhắc nhở đánh giá trước
  2. Chọn một số ngày từ danh sách

Kết quả mong muốn:
  Số ngày được chọn hiển thị trong ô input

--------------------------------------------------------------------------------

Test Case #20
----------------------------------------
ID: TC_FUN_028
Mục đích kiểm thử: Kiểm tra chức năng Lưu thông tin thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Nhập đầy đủ thông tin hợp lệ vào form
  2. Nhấn btn Lưu

Kết quả mong muốn:
  Thông tin thử việc được lưu thành công và quay trở lại màn hình list thử việc

--------------------------------------------------------------------------------

Test Case #21
----------------------------------------
ID: TC_FUN_029
Mục đích kiểm thử: Kiểm tra chức năng Hủy tạo mới thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Nhấn btn Hủy

Kết quả mong muốn:
  Hủy tạo mới thử việc và quay trở lại màn hình list thử việc

--------------------------------------------------------------------------------



============================================================
MÀN HÌNH CHỈNH SỬA THỬ VIỆC
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_030
Mục đích kiểm thử: Kiểm tra hiển thị màn hình chỉnh sửa thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập vào trang chỉnh sửa thông tin thử việc

Kết quả mong muốn:
  Màn hình hiển thị form chỉnh sửa thông tin thử việc với các trường: Thông tin nhân sự thử việc, Chọn quản lý trực tiếp, Chọn người theo dõi, Ngày bắt đầu thử việc, Ngày kết thúc thử việc, Btn Thêm giai đoạn, Btn Xóa giai đoạn, Chọn người đánh giá, Chọn mẫu đánh giá, Cài đặt trọng số chấm điểm, Input nhập trọng số, Hạn đánh giá, Hạn phê duyệt, Chọn kết quả mặc định, Gửi thông báo nhắc nhở đánh giá trước, Btn Lưu, Btn Hủy

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_UI_031
Mục đích kiểm thử: Kiểm tra trạng thái ban đầu của các button trên màn chỉnh sửa thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập vào trang chỉnh sửa thông tin thử việc

Kết quả mong muốn:
  Các button đều enable , btn Lưu cập nhật disable , các trường thông tin ở giai đoạn thử việc đã có kết quả đánh giá/phê duyệt thì disable

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_FUN_032
Mục đích kiểm thử: Kiểm tra chức năng Lưu thông tin thử việc sau chỉnh sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Thay đổi thông tin trên form
  2. Nhấn btn Lưu

Kết quả mong muốn:
  Thông tin thử việc được cập nhật thành công và quay trở lại màn hình list thử việc

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: TC_FUN_033
Mục đích kiểm thử: Kiểm tra btn Xóa giai đoạn chỉ hiện ở những giai đoạn nào chưa có kết quả đánh giá/phê duyệt
Độ ưu tiên: High

Các bước thực hiện:
  1. Truy cập vào trang chỉnh sửa thông tin thử việc
  2. Kiểm tra các giai đoạn

Kết quả mong muốn:
  Btn Xóa giai đoạn chỉ hiện ở những giai đoạn nào chưa có kết quả đánh giá/phê duyệt

--------------------------------------------------------------------------------



============================================================
MÔ TẢ LOGIC
============================================================

Test Case #1
----------------------------------------
ID: TC_LOGIC_001
Mục đích kiểm thử: Kiểm tra chỉ được phép chọn duy nhất 1 quản lý trực tiếp
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn nhiều quản lý trực tiếp

Kết quả mong muốn:
  Hệ thống chỉ cho phép chọn 1 quản lý trực tiếp

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_LOGIC_002
Mục đích kiểm thử: Kiểm tra quản lý trực tiếp có quyền chỉnh sửa chi tiết thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập với vai trò quản lý trực tiếp
  2. Truy cập vào chi tiết thử việc của nhân viên

Kết quả mong muốn:
  Quản lý trực tiếp có quyền chỉnh sửa chi tiết thử việc

--------------------------------------------------------------------------------

Test Case #3
----------------------------------------
ID: TC_LOGIC_003
Mục đích kiểm thử: Kiểm tra quản lý trực tiếp là người phê duyệt giai đoạn
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập với vai trò quản lý trực tiếp
  2. Truy cập vào giai đoạn thử việc của nhân viên

Kết quả mong muốn:
  Quản lý trực tiếp có quyền phê duyệt giai đoạn

--------------------------------------------------------------------------------

Test Case #4
----------------------------------------
ID: TC_LOGIC_004
Mục đích kiểm thử: Kiểm tra người theo dõi có quyền xem chi tiết thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đăng nhập với vai trò người theo dõi
  2. Truy cập vào chi tiết thử việc của nhân viên

Kết quả mong muốn:
  Người theo dõi có quyền xem chi tiết thử việc

--------------------------------------------------------------------------------

Test Case #5
----------------------------------------
ID: TC_LOGIC_005
Mục đích kiểm thử: Kiểm tra người theo dõi sẽ nhận được thông báo khi có người cmt vào thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Thêm người theo dõi vào thông tin thử việc
  2. Người khác comment vào thông tin thử việc
  3. Kiểm tra thông báo của người theo dõi

Kết quả mong muốn:
  Người theo dõi nhận được thông báo khi có người comment vào thử việc

--------------------------------------------------------------------------------

Test Case #6
----------------------------------------
ID: TC_LOGIC_006
Mục đích kiểm thử: Kiểm tra hết hạn đánh giá người đánh giá sẽ không được đánh giá nhân sự thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đặt hạn đánh giá trong quá khứ
  2. Đăng nhập với vai trò người đánh giá
  3. Truy cập vào đánh giá nhân sự thử việc

Kết quả mong muốn:
  Người đánh giá không được đánh giá nhân sự thử việc

--------------------------------------------------------------------------------

Test Case #7
----------------------------------------
ID: TC_LOGIC_007
Mục đích kiểm thử: Kiểm tra hết hạn phê duyệt quản lý trực tiếp sẽ không được phê duyệt giai đoạn nhân sự thử việc
Độ ưu tiên: High

Các bước thực hiện:
  1. Đặt hạn phê duyệt trong quá khứ
  2. Đăng nhập với vai trò quản lý trực tiếp
  3. Truy cập vào phê duyệt giai đoạn nhân sự thử việc

Kết quả mong muốn:
  Quản lý trực tiếp không được phê duyệt giai đoạn nhân sự thử việc

--------------------------------------------------------------------------------

Test Case #8
----------------------------------------
ID: TC_LOGIC_008
Mục đích kiểm thử: Kiểm tra hệ thống tự động phê duyệt Pass giai đoạn khi hết hạn phê duyệt và chọn Pass
Độ ưu tiên: High

Các bước thực hiện:
  1. Đặt hạn phê duyệt trong quá khứ
  2. Chọn kết quả mặc định Pass
  3. Chờ hết hạn phê duyệt

Kết quả mong muốn:
  Hệ thống tự động phê duyệt Pass giai đoạn và chuyển sang giai đoạn tiếp theo

--------------------------------------------------------------------------------

Test Case #9
----------------------------------------
ID: TC_LOGIC_009
Mục đích kiểm thử: Kiểm tra hệ thống tự động phê duyệt Fail giai đoạn khi hết hạn phê duyệt và chọn Fail
Độ ưu tiên: High

Các bước thực hiện:
  1. Đặt hạn phê duyệt trong quá khứ
  2. Chọn kết quả mặc định Fail
  3. Chờ hết hạn phê duyệt

Kết quả mong muốn:
  Hệ thống tự động phê duyệt Fail giai đoạn và tự động kết thúc quá trình thử việc của nhân sự , chuyển nhân sự sang tab ending

--------------------------------------------------------------------------------

Test Case #10
----------------------------------------
ID: TC_LOGIC_010
Mục đích kiểm thử: Kiểm tra gửi thông báo nhắc nhở đánh giá trước hạn đánh giá
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn số ngày Gửi thông báo nhắc nhở đánh giá trước
  2. Kiểm tra thông báo của người đánh giá trước hạn đánh giá

Kết quả mong muốn:
  Người đánh giá nhận được thông báo nhắc nhở đánh giá trước hạn đánh giá

--------------------------------------------------------------------------------

Test Case #11
----------------------------------------
ID: TC_LOGIC_011
Mục đích kiểm thử: Kiểm tra chỉ hiện trọng số cho các câu hỏi cố định Đánh giá sự phù hợp với tổ chức hoặc Đánh giá sự phù hợp với công việc, hoặc cả 2 phụ thuộc vào biểu mẫu đánh giá được chọn ở mục 11
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn mẫu đánh giá ở mục 11
  2. Ấn btn Cài đặt trọng số chấm điểm

Kết quả mong muốn:
  Sẽ chỉ hiện trọng số cho các câu hỏi cố định Đánh giá sự phù hợp với tổ chức hoặc Đánh giá sự phù hợp với công việc, hoặc cả 2 phụ thuộc vào biểu mẫu đánh giá được chọn ở mục 11

--------------------------------------------------------------------------------


Total Test Cases Generated: 51
Generated by Test Case Generator v1.0
