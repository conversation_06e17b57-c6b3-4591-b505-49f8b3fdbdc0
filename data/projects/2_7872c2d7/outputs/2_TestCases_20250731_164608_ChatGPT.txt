================================================================================
TEST CASES - 2
Generated on: 2025-07-31 16:46:08
================================================================================


============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_051
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_052
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_053
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_054
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_055
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_056
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE TÌM KIẾM
============================================================



============================================================
KIỂM TRA CHỨC NĂNG TÌM KIẾM
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_057
Mục đích kiểm thử: Kiểm tra tìm kiếm với từ khóa chính xác
Độ ưu tiên: High

Các bước thực hiện:
  1. Nhập từ khóa chính xác vào ô tìm kiếm
  2. Nhấn Enter hoặc nút Tìm kiếm

Kết quả mong muốn:
  Hiển thị danh sách kết quả khớp với từ khóa

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_058
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_059
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_060
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_061
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_062
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_063
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_064
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_065
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_066
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_067
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_068
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_069
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_070
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_071
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_072
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_073
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_074
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_075
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_076
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_077
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_078
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_079
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_080
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_081
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_082
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_083
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_084
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_085
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_086
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_087
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_088
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_089
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_090
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_091
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_092
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_093
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_094
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_095
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_096
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_097
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_098
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_099
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_100
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================


Total Test Cases Generated: 50
Generated by Test Case Generator v1.0
